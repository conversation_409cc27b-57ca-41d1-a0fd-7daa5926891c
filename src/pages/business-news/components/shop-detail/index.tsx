import React from 'react';
import { ReplayShopTable } from '@/components/replay-shop-table';
import { canRequest } from '@/utils/review-board';
import { Form, FormInstance, TableColumnType } from 'antd';
import { ExportButton } from '@alife/mp-oss-upload';
import { formatCompareValue } from '@/common/utils';
import { HiddenFieldsWrap } from '../weekly-report-hidden-field';
import { getFieldsMap } from '../handle-data/datas';
import { useSessionStorageState } from 'ahooks';
import { useDataState } from '../store/useDataState';
import { formatterTargetValue, MONEY_UNIT } from '../utils';

interface IProps {
  form: FormInstance;
  pid: string;
  fields: any[];
  shopData: any[];
  isFood: boolean;
}

const ShopDetail: React.FC<IProps> = (props: IProps) => {
  const { form, pid, fields: shopFields, shopData = [], isFood } = props;
  const { shopIdList, dateRange } = useDataState();

  const hiddenFields = Form.useWatch('hiddenFields', form);
  const clickable = canRequest({ pid, shopIds: shopIdList, dateRange });
  const newShopData = shopData.slice(0, 10);
  const [privateHiddenFields, setPrivateHiddenFields] = useSessionStorageState(
    'businessNewsShopDetailHiddenFields',
    {
      defaultValue: [],
    },
  );

  const basicColumns = [
    { title: '门店名称', dataIndex: 'shop_name', width: 160, render: (val) => val || '-' },
    { title: '城市', dataIndex: 'city_name', width: 80, render: (val) => val || '-' },
    { title: '区域', dataIndex: 'district_name', width: 80, render: (val) => val || '-' },
  ];
  const extColumns: TableColumnType[] = shopFields.map((item) => ({
    title: item.title,
    dataIndex: item.key,
    render: (value, record) => {
      const compareValue = record[`${item.key}_last_cycle`];
      const { title } = item;
      if (value) {
        value = formatterTargetValue(
          value,
          title?.endsWith(MONEY_UNIT),
          title?.endsWith('占比') || title?.endsWith('率'),
        );
        let compareRender = null;
        if (compareValue !== undefined && compareValue !== null && compareValue !== '-') {
          const [compareColor, arrow, parsedCompareValue] = formatCompareValue(compareValue);
          // 环比为空和'--'文案都不展示环比
          if (parsedCompareValue === '--') {
            compareRender = null;
          } else {
            compareRender = (
              <span style={{ color: compareColor, fontSize: 12, marginLeft: 4 }}>
                {arrow}
                {parsedCompareValue}
              </span>
            );
          }
        }
        return (
          <div>
            {value}
            {compareRender}
          </div>
        );
      }
      return '-';
    },
  }));

  const columns = [...basicColumns, ...extColumns]?.filter(
    (item) => !(hiddenFields || []).concat(privateHiddenFields)?.includes(item.dataIndex),
  );
  const updateHiddenFieldsClick = (fieldMap) => {
    if (privateHiddenFields.includes(fieldMap.key)) {
      setPrivateHiddenFields(privateHiddenFields.filter((item) => item !== fieldMap.key));
    } else {
      setPrivateHiddenFields([...privateHiddenFields, fieldMap.key]);
    }
  };

  return (
    <>
      <div style={{ margin: '20px 0px 10px 0px' }}>
        <span style={{ fontSize: 16, fontWeight: 800 }}>门店明细:</span>
        {shopIdList?.length > 1 && dateRange?.length ? (
          <ExportButton
            bizCode="xibao_shop_download_0819"
            style={{ marginLeft: 10 }}
            disabled={!clickable}
            modalProps
            requestParams={{
              bizParam: JSON.stringify({
                shopIdList: shopIdList.join(','),
                pid,
                businessNewType: isFood ? 'FOOD' : 'OTHER',
                startDate: dateRange[0].format('YYYYMMDD'),
                endDate: dateRange[1].format('YYYYMMDD'),
              }),
            }}
          >
            下载明细表
          </ExportButton>
        ) : null}
        {shopIdList?.length > 10 && (
          <span style={{ marginLeft: 10, fontSize: 10 }}>
            最多展示10家门店明细，更多数据请下载明细表查看
          </span>
        )}
      </div>

      <ReplayShopTable
        tableProps={{
          columns,
          dataSource: newShopData,
        }}
        fields={[]}
        type="store"
      />
      {shopIdList?.length > 10 && (
        <div id="shop-detail-extra" style={{ marginTop: 10 }}>
          备注：因门店数量较多，其他门店数据请联系您的专属运营人员获取。
        </div>
      )}
    </>
  );
};

export default ShopDetail;
