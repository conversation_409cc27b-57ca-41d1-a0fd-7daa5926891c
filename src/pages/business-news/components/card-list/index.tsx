import styled from 'styled-components';
import DataCard from './card';
import { IBusinessData } from '../const';

const Container = styled.div``;
interface IProps {
  hiddenFields: string[];
  data: IBusinessData;
}
const filter = (fields: any[], hiddenFields: string[]) => {
  const newFields = JSON.parse(JSON.stringify(fields));
  return newFields
    .map((item) => {
      if (item?.children) {
        item.children = filter(item.children, hiddenFields);
      }
      if (item?.expandChildrenData) {
        item.expandChildrenData = filter(item.expandChildrenData, hiddenFields);
      }
      if (item?.key) {
        if (hiddenFields.includes(item.key)) {
          return null;
        }
      }
      return item;
    })
    .filter((item) => {
      if (item) {
        if (item.children) {
          return !!item.children.length;
        }
        return true;
      } else {
        return false;
      }
    });
};
export default function CardList(props: IProps) {
  const { hiddenFields, data } = props;
  const fields = data?.fields || [];
  const filteredFields = filter(fields, hiddenFields);
  return (
    <Container>
      {filteredFields.map((item) => {
        return <DataCard data={item} isMainCard />;
      })}
    </Container>
  );
}
